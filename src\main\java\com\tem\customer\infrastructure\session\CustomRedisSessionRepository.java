package com.tem.customer.infrastructure.session;

import com.iplatform.common.utils.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.serializer.SerializationException;
import org.springframework.session.data.redis.RedisIndexedSessionRepository;

/**
 * 自定义Redis Session仓库
 * 用于捕获和处理Session反序列化异常
 * 
 * <AUTHOR>
 * @since 2025-07-28
 */
@Slf4j
public class CustomRedisSessionRepository extends RedisIndexedSessionRepository {

    private final RedisOperations<String, Object> sessionRedisOperations;

    public CustomRedisSessionRepository(RedisOperations<String, Object> sessionRedisOperations) {
        super(sessionRedisOperations);
        this.sessionRedisOperations = sessionRedisOperations;
    }

    @Override
    public RedisIndexedSessionRepository.RedisSession findById(String id) {
        try {
            return super.findById(id);
        } catch (SerializationException e) {
            LogUtils.warn(log, "Session反序列化失败，sessionId: {}, 错误: {}", id, e.getMessage());
            // 删除有问题的session
            deleteById(id);
            return null;
        } catch (Exception e) {
            if (e.getMessage() != null && e.getMessage().contains("InvalidClassException")) {
                LogUtils.warn(log, "Session版本不兼容，sessionId: {}, 错误: {}", id, e.getMessage());
                // 删除有问题的session
                deleteById(id);
                return null;
            }
            throw e;
        }
    }

    @Override
    public void deleteById(String id) {
        try {
            super.deleteById(id);
            LogUtils.info(log, "已删除有问题的session: {}", id);
        } catch (Exception e) {
            LogUtils.error(log, "删除session失败: {}", id, e);
        }
    }
}
