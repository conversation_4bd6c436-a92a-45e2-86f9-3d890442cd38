package com.tem.customer.controller.partner

import com.iplatform.common.Config
import com.iplatform.common.ResponseDto
import com.tem.customer.BaseControllerSpec
import com.tem.customer.model.vo.common.SpecialLoginVO
import com.tem.customer.service.partner.PartnerUserService
import com.tem.customer.shared.common.ResultCode
import com.tem.customer.shared.exception.BusinessException
import com.tem.customer.shared.utils.UserContextUtil
import com.tem.otapub.share.api.ProvidersRelationshipService
import com.tem.platform.api.ConfigService
import com.tem.platform.api.PartnerService
import com.tem.platform.api.PermissionService
import com.tem.platform.api.UserService
import com.tem.platform.api.dto.PartnerChannelConfigDto
import com.tem.platform.api.dto.UserDto
import com.tem.pss.api.ServerSkillService
import com.tem.sso.api.SSOService
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.bean.override.mockito.MockitoBean
import spock.lang.Subject
/**
 * 企业用户控制器测试类
 * 使用Spock框架进行单元测试
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@SpringBootTest
class PartnerUserControllerSpec extends BaseControllerSpec {

    @Subject
    PartnerUserController partnerUserController

    @MockitoBean
    PartnerUserService partnerUserService = Mock()

    @MockitoBean
    ProvidersRelationshipService providersRelationshipService = Mock()

    @MockitoBean
    UserService userService = Mock()

    @MockitoBean
    ServerSkillService serverSkillService = Mock()

    @MockitoBean
    PartnerService partnerService = Mock()

    @MockitoBean
    PermissionService permissionService = Mock()

    @MockitoBean
    SSOService ssoService = Mock()

    @MockitoBean
    ConfigService configService = Mock()

    MockedStatic<Config> configMockStatic

    def setup() {
        partnerUserController = new PartnerUserController(partnerUserService)

        // 通过反射设置Dubbo服务
        setDubboService(partnerUserController, "providersRelationshipService", providersRelationshipService)
        setDubboService(partnerUserController, "userService", userService)
        setDubboService(partnerUserController, "serverSkillService", serverSkillService)
        setDubboService(partnerUserController, "partnerService", partnerService)
        setDubboService(partnerUserController, "permissionService", permissionService)
        setDubboService(partnerUserController, "ssoService", ssoService)
        setDubboService(partnerUserController, "configService", configService)

        // 设置静态方法Mock - 只需要Config，UserContextUtil已在基类中处理
        configMockStatic = Mockito.mockStatic(Config.class)
    }

    def cleanup() {
        configMockStatic?.close()
    }

    /**
     * 通过反射设置Dubbo服务
     */
    private void setDubboService(Object target, String fieldName, Object service) {
        def field = target.class.getDeclaredField(fieldName)
        field.setAccessible(true)
        field.set(target, service)
    }

    def "测试查询企业用户列表 - 关键字为空"() {
        when: "调用查询接口，关键字为空"
        def result = partnerUserController.listUsers("")

        then: "返回空列表"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.userList == null || result.data.userList.isEmpty()
    }

    def "测试查询企业用户列表 - 关键字为null"() {
        when: "调用查询接口，关键字为null"
        def result = partnerUserController.listUsers(null)

        then: "返回空列表"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.userList == null || result.data.userList.isEmpty()
    }

    def "测试查询企业用户列表 - 关键字为空白字符"() {
        when: "调用查询接口，关键字为空白字符"
        def result = partnerUserController.listUsers("   ")

        then: "返回空列表"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.userList == null || result.data.userList.isEmpty()
    }

    def "测试查询企业用户列表 - 正常查询成功"() {
        given: "准备测试数据"
        def keyword = "张三"
        def tmcId = 1001L
        def relationships = []

        and: "Mock静态方法调用"
        Mockito.when(UserContextUtil.getCurrentUserPartnerId()).thenReturn(tmcId)

        and: "Mock服务调用"
        providersRelationshipService.getRelationshipsByTmcId(tmcId) >> ResponseDto.success(relationships)

        when: "调用查询接口"
        def result = partnerUserController.listUsers(keyword)

        then: "验证返回结果"
        result != null
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data.userList == null || result.data.userList.isEmpty()
    }

    def "测试代客登录 - 参数验证失败"() {
        when: "调用代客登录接口，企业ID为空"
        partnerUserController.specialLogin(null, 1001L, null, null)

        then: "抛出业务异常"
        thrown(BusinessException)
    }

    def "测试代客登录 - 用户ID为空"() {
        when: "调用代客登录接口，用户ID为空"
        partnerUserController.specialLogin(1001L, null, null, null)

        then: "抛出业务异常"
        thrown(BusinessException)
    }

    def "测试代客登录 - 获取用户信息失败"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def userId = 2001L

        and: "Mock服务调用返回失败"
        userService.getUserBaseInfo(partnerId, userId) >> ResponseDto.error("用户不存在")

        when: "调用代客登录接口"
        partnerUserController.specialLogin(partnerId, userId, null, null)

        then: "抛出业务异常"
        thrown(BusinessException)
    }

    def "测试代客登录 - 正常登录成功"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def userId = 2001L
        def currentUserId = 3001L
        def h5 = true  // Boolean类型，不是String
        def service = "order"

        // 准备用户信息
        def userDto = new UserDto()
        userDto.setId(userId)
        userDto.setPartnerId(partnerId)
        userDto.setFullname("张三")
        userDto.setMobile("***********")

        // 准备渠道配置
        def channelConfig = new PartnerChannelConfigDto()
        channelConfig.setDomainH5("test.example.com")

        and: "Mock静态方法调用"
        Mockito.when(UserContextUtil.getCurrentUserId()).thenReturn(currentUserId)

        and: "Mock服务调用"
        userService.getUserBaseInfo(partnerId, userId) >> ResponseDto.success(userDto)
        configService.getPartnerChannelConfigDto(partnerId) >> ResponseDto.success(channelConfig)
        ssoService.getLoginAuthToken(_, _) >> ResponseDto.success("mock-auth-token")

        when: "调用代客登录接口"
        def result = partnerUserController.specialLogin(partnerId, userId, h5, service)

        then: "验证返回结果"
        result.code == ResultCode.SUCCESS.getCode()
        result.data != null
        result.data instanceof SpecialLoginVO
        result.data.authToken == "mock-auth-token"
        result.data.h5Url.contains("test.example.com")
        result.data.userName == "张三"
        result.data.mobile == "***********"
    }

    def "测试获取用户基础信息 - 用户ID为空"() {
        when: "调用获取用户基础信息，用户ID为空"
        def result = partnerUserController.getUserBaseInfoPartnerId(1001L, null)

        then: "返回错误响应"
        result.code == ResultCode.BAD_REQUEST.getCode()
    }

    def "测试获取用户基础信息 - 正常获取成功"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def userId = 2001L
        def userDto = new UserDto()
        userDto.setId(userId)
        userDto.setPartnerId(partnerId)
        userDto.setFullname("张三")

        and: "Mock服务调用"
        userService.getUserBaseInfo(partnerId, userId) >> ResponseDto.success(userDto)

        when: "调用获取用户基础信息"
        def result = partnerUserController.getUserBaseInfoPartnerId(partnerId, userId)

        then: "验证返回结果"
        result.success
        result.data != null
        result.data.id == userId
        result.data.partnerId == partnerId
        result.data.fullname == "张三"
    }

    def "测试获取用户基础信息 - 服务异常"() {
        given: "准备测试数据"
        def partnerId = 1001L
        def userId = 2001L

        and: "Mock服务调用抛出异常"
        userService.getUserBaseInfo(partnerId, userId) >> { throw new RuntimeException("服务异常") }

        when: "调用获取用户基础信息"
        def result = partnerUserController.getUserBaseInfoPartnerId(partnerId, userId)

        then: "返回错误响应"
        result.code == ResultCode.INTERNAL_SERVER_ERROR.getCode()
    }
}
